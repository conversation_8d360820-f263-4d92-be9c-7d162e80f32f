<template>
  <el-container class="page-container">
    <el-aside style="width: 268px; height: 100vh" v-loading="treeLoading">
      <div class="toolbarTree-wrapper" style="height: calc(100% - 12px)">
        <div class="searchPart">
          <el-input
            placeholder="输入关键字进行过滤"
            v-model="treeFilterText"
            size="small"
            class="filterInput"
            @input="handleSearch"
          >
            <i class="el-icon-search el-input__icon" slot="suffix"> </i>
          </el-input>
        </div>
        <div class="searchTotal" v-show="!treeLoading">
          <span class="totalName"> 全部系统</span>
          <span class="totalNum">{{ totalSystem }}个</span>
        </div>

        <el-scrollbar style="height: 100%; flex: 1" v-show="!treeLoading">
          <el-tree
            v-if="isShowTree"
            style="width: 98%"
            :key="treeFilterText"
            class="icsp-el-tree-2"
            ref="treeRef"
            node-key="uuid"
            highlight-current
            accordion
            :data="treeData"
            :props="defaultProps"
            :indent="14"
            @node-click="handleNodeClick"
            icon-class="el-icon-caret-right"
          >
            <span
              :class="[
                'custom-tree-node',
                node.level === 2 ? 'marginLeft10' : ''
              ]"
              slot-scope="{ node, data }"
            >
              <span class="custom-tree-name" :title="data.text">
                {{ data.text }}
              </span>
              <span class="custom-tree-btn">
                <el-tag
                  size="mini"
                  :type="
                    node.level === 1
                      ? 'success'
                      : node.level === 2
                      ? ''
                      : 'info'
                  "
                  >{{
                    node.level === 1
                      ? '系统'
                      : node.level === 2
                      ? '模型'
                      : node.data.nodeType
                  }}</el-tag
                >
              </span>
            </span>
          </el-tree>
        </el-scrollbar>
      </div>
    </el-aside>
    <!-- 垂直居中的拖拽分割条 -->
    <SplitBar
      :value="268"
      :min="270"
      :max="500"
      :size="10"
      direction="row"
      id="catalog-sidebar"
      class="catalog-split-bar"
    />

    <el-container>
      <el-header class="page-header">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            v-for="(item, index) in breadcrumbList"
            :key="item.id"
            style="cursor: pointer"
            ><span @click="handleNavigate(item, index)">{{
              item.text
            }}</span></el-breadcrumb-item
          >
        </el-breadcrumb>
      </el-header>
      <el-main
        v-loading="loading"
        style="
          position: relative;
          overflow: hidden;
          background-color: #fff;
          margin-left: 16px;
          padding: 20px;
        "
      >
        <el-scrollbar wrap-class="scrollbar-wrapper-y" style="height: 100%">
          <div
            style="
              margin-bottom: 10px;
              position: absolute;
              right: 12px;
              top: 2px;
            "
            v-if="
              selectedNode.type === 'model' || selectedNode.type === 'entity'
            "
          >
            <el-radio-group
              v-model="radio"
              @change="handleRadioChange"
              class="chartOrTable"
            >
              <el-radio-button
                v-show="radio === '返回列表' && selectedNode.type === 'model'"
                label="图形化界面展示"
              ></el-radio-button>
              <el-radio-button
                v-show="radio === '图形化界面展示'"
                label="返回列表"
              ></el-radio-button>
            </el-radio-group>
          </div>

          <template
            v-if="
              selectedNode.id === '0' ||
              (['system', 'model'].includes(selectedNode.type) &&
                radio === '返回列表')
            "
          >
            <div class="statisticsCont">
              <div class="statisticsCont-top" v-loading="treeLoading">
                <div
                  class="workItem"
                  v-for="item in statisticsDataCopy"
                  :key="item.name"
                >
                  <div class="content">
                    <div class="name">{{ item.name }}</div>
                    <div class="count">
                      <span>{{ item.count }}</span>
                      <span>个</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="statisticsCont-bottom" v-loading="loading">
                <template v-if="selectedNode.id === '0'">
                  <div class="chartCont" style="position: relative">
                    <Chart title="系统库表数量Top6" ref="chartRef1" />
                  </div>

                  <div class="chartCont" style="position: relative">
                    <LineChart
                      title="实体变化趋势"
                      ref="chartRef2"
                      style="width: 100%; height: 100%"
                    />

                    <el-select
                      style="
                        width: 160px;
                        position: absolute;
                        top: -4px;
                        right: 10px;
                        z-index: 10;
                      "
                      v-model="countType"
                      @change="handleCountTypeChange"
                    >
                      <el-option label="按天" value="d"></el-option>
                      <el-option label="按月" value="m"></el-option>
                    </el-select>
                  </div>
                </template>

                <div v-if="selectedNode.type === 'system'" style="width: 100%">
                  <el-table
                    v-if="tableIsShow"
                    ref="systemTableRef"
                    key="system"
                    :data="catalogList"
                    border
                    fit
                    stripe
                    highlight-current-row
                  >
                    <el-table-column
                      prop="text"
                      label="模型名称"
                      min-width="180"
                      header-align="center"
                      align="center"
                      show-overflow-tooltip
                    >
                      <template slot-scope="{ row }">
                        <span
                          @mouseover="
                            (event) => {
                              event.target.style.textDecoration = 'underline'
                              event.target.style.color = '#0e88eb'
                              event.target.style.cursor = 'pointer'
                            }
                          "
                          @mouseout="
                            (event) => {
                              event.target.style.textDecoration = 'none'
                              event.target.style.color = '#606266'
                              event.target.style.cursor = 'default'
                            }
                          "
                          @click.stop="handleClickModelName(row)"
                          >{{ row.text }}</span
                        >
                      </template>
                    </el-table-column>

                    <el-table-column
                      prop="tableTotal"
                      label="库表数"
                      min-width="180"
                      align="right"
                      header-align="center"
                      show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                      prop="viewTotal"
                      label="视图数"
                      min-width="180"
                      align="right"
                      header-align="center"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="columnTotal"
                      label="字段数"
                      min-width="180"
                      align="right"
                      header-align="center"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="buildState"
                      label="建表状态"
                      min-width="180"
                      header-align="center"
                      align="center"
                      show-overflow-tooltip
                    >
                      <template slot-scope="{ row }">
                        {{
                          row.buildState === '1'
                            ? '成功'
                            : row.buildState === '2'
                            ? '无建表'
                            : row.buildState === '0'
                            ? '失败'
                            : '建表中'
                        }}
                      </template>
                    </el-table-column>
                  </el-table>
                  <!-- <el-pagination
                    style="
                      display: flex;
                      justify-content: flex-end;
                      margin: 10px 12px 0 0;
                    "
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryparams.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="queryparams.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                  >
                  </el-pagination> -->
                </div>

                <div v-if="selectedNode.type === 'model'" style="width: 100%">
                  <el-table
                    v-if="tableIsShow"
                    ref="modelTableRef"
                    key="model"
                    :data="entityDtoList"
                    border
                    fit
                    stripe
                    highlight-current-row
                  >
                    <el-table-column
                      prop="entityName"
                      label="实体名称"
                      min-width="180"
                      header-align="center"
                      align="center"
                      show-overflow-tooltip
                    >
                      <template slot-scope="{ row }">
                        <span
                          @mouseover="
                            (event) => {
                              event.target.style.textDecoration = 'underline'
                              event.target.style.color = '#0e88eb'
                              event.target.style.cursor = 'pointer'
                            }
                          "
                          @mouseout="
                            (event) => {
                              event.target.style.textDecoration = 'none'
                              event.target.style.color = '#606266'
                              event.target.style.cursor = 'default'
                            }
                          "
                          @click.stop="handleClickTableName(row)"
                          >{{ row.entityName }}</span
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="enetityCode"
                      label="实体英文名"
                      min-width="180"
                      align="center"
                      header-align="center"
                      show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                      prop="entityType"
                      label="类型"
                      min-width="100"
                      align="center"
                      header-align="center"
                      show-overflow-tooltip
                    >
                    </el-table-column>

                    <el-table-column
                      prop="fieldCnt"
                      label="字段数"
                      min-width="100"
                      align="right"
                      header-align="center"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="rowsCount"
                      label="数据量"
                      min-width="100"
                      align="right"
                      header-align="center"
                      show-overflow-tooltip
                    >
                      <template slot-scope="{ row }">
                        {{ row.rowsCount || 0 }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="remark"
                      label="摘要"
                      min-width="130"
                      header-align="center"
                      align="left"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="createTime"
                      label="创建时间"
                      header-align="center"
                      min-width="160"
                      align="center"
                      show-overflow-tooltip
                    >
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </template>

          <template
            v-if="radio === '返回列表' && selectedNode.type === 'entity'"
          >
            <div class="data-cont-wrap">
              <div class="line1">
                <div class="el-collapse-title">
                  <div class="ellis" :title="selectedNode.text">
                    {{ selectedNode.text }}
                  </div>

                  <div class="info-list">{{ selectedNode.nodeType }}</div>
                </div>
              </div>

              <div class="listCont">
                <div
                  class="listItem"
                  style="cursor: default; border: none; padding: 0"
                >
                  <div class="line2">
                    <div class="itemMes">
                      <span class="message"
                        >来源系统：<span>{{
                          selectedNode.systemName
                        }}</span></span
                      >

                      <span class="message"
                        >建表状态：<span
                          :class="
                            selectedNode.buildTable === '1'
                              ? 'success'
                              : selectedNode.buildTable === '2'
                              ? 'error'
                              : 'normal'
                          "
                          >{{
                            selectedNode.buildTable === '1'
                              ? '成功'
                              : selectedNode.buildTable === '2'
                              ? '失败'
                              : selectedNode.buildTable === '0'
                              ? '不建对象'
                              : '未发起建表'
                          }}</span
                        ></span
                      >
                      <span class="message"
                        >数据量：<span
                          style="font-size: 18px; font-weight: 700"
                          >{{ selectedNode.rowsCount }}</span
                        ></span
                      >
                      <span class="message"
                        >创建时间：<span>{{
                          selectedNode.createTime
                        }}</span></span
                      >
                      <span class="message"
                        >实体英文名：<span>{{
                          selectedNode.enetityCode
                        }}</span></span
                      >
                      <span class="message"
                        >模型名称：<span
                          style="
                            text-decoration: underline;
                            cursor: pointer;
                            color: #3572ff;
                          "
                          @click="handleToView"
                          >{{ selectedNode.modelName }}</span
                        ></span
                      >
                    </div>

                    <div
                      style="width: 100%"
                      v-if="selectedNode.nodeTypeCode === 'table'"
                    >
                      <el-table
                        v-if="tableIsShow"
                        ref="columnTableRef"
                        key="table"
                        :data="columnDtoList"
                        border
                        fit
                        stripe
                        highlight-current-row
                      >
                        <el-table-column
                          prop="itemName"
                          label="信息项名称"
                          min-width="180"
                          header-align="center"
                          align="center"
                          show-overflow-tooltip
                        >
                        </el-table-column>
                        <el-table-column
                          prop="itemCode"
                          label="字段名"
                          min-width="140"
                          align="center"
                          header-align="center"
                          show-overflow-tooltip
                        >
                        </el-table-column>
                        <el-table-column
                          prop="dataTypeCode"
                          label="数据类型"
                          min-width="130"
                          header-align="center"
                          align="center"
                          show-overflow-tooltip
                        >
                        </el-table-column>

                        <el-table-column
                          prop="dataLength"
                          label="数据长度"
                          min-width="130"
                          header-align="center"
                          align="center"
                          show-overflow-tooltip
                        ></el-table-column>

                        <el-table-column
                          prop="itemDesc"
                          label="信息项描述"
                          header-align="center"
                          min-width="180"
                          align="left"
                          show-overflow-tooltip
                        >
                        </el-table-column>
                      </el-table>
                    </div>

                    <div v-else class="sql">
                      <div>语句：</div>
                      <el-input
                        class="sql-input"
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 20 }"
                        :value="selectedNode.sql"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <div
            v-if="radio === '图形化界面展示'"
            id="iframe-chart"
            class="iframe-wrap"
          >
            <iframe
              ref="iframeRef"
              :src="iframeUrl"
              frameborder="none"
              id="iframe_id"
              class="iframe"
            ></iframe>
          </div>
        </el-scrollbar>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import {
  reactive,
  ref,
  getCurrentInstance,
  nextTick,
  onActivated,
  computed
} from 'vue'
import Chart from './chart'
import LineChart from './lineChart'
import Cookies from 'js-cookie'
import {
  queryPhysicalModuleTableList,
  queryColByEntityIds,
  querySystemTree,
  queryStatistics,
  queryCatalogStatistics,
  countTimeSpanBuildObjs,
  queryEntityAndColNum,
  queryEntityDetail
} from 'biz/http/api'
import moment from 'moment'
import { debounce } from 'lodash-es'
import SplitBar from '@/core/components/SplitBar'

const iframeUrl = ref(config.iframeToDateModel)
const { proxy } = getCurrentInstance()

const defaultProps = reactive({
  children: 'children',
  label: 'text',
  isLeaf: (data, node) => {
    return !data.total
  }
})
const treeFilterText = ref('')

const treeData = ref([])

// 加载树数据
const totalSystem = ref(0)
const isShowTree = ref(true) // 重置树的渲染
const modelTableRef = ref(null)

/** 懒加载版本 废弃 */
const handleLoadChildrenData = async (node, resolve) => {
  const { code, data } =
    node.level < 2
      ? await querySystemTree(
          node.level
            ? {
                type: 'mod',
                parentId: node.data.id
              }
            : ''
        )
      : await queryPhysicalModuleTableList({
          keepPrev: '0', // 0 获取最新版本，1 获取发布和最新版本， 2 获取发布版本
          modelIds: [node.data.modelId]
        })

  if (code === '200') {
    if (node.level === 0) {
      totalSystem.value = data.length
      const tempData = data.map((i) => {
        return {
          ...i,
          type: 'system'
        }
      })
      return resolve(tempData)
    }
    if (node.level === 1) {
      const tempData = data.map((i) => {
        return {
          ...i,
          id: i.id + i.parentId,
          modelId: i.id,
          type: 'model'
        }
      })
      return resolve(tempData)
    }

    if (node.level === 2) {
      const tempData = data[0].entityDtoList.map((i) => {
        return {
          ...i,
          text: i.entityName,
          id: i.entityId,
          type: 'entity',
          total: data[0].entityCount,
          modelId: data[0].modelId,
          modelName: node.data.text,
          systemName: node.parent.data.text
        }
      })
      tableIsShow.value = false
      entityDtoList.value = data[0].entityDtoList
      nextTick(() => {
        modelTableRef.value && modelTableRef.value.doLayout()
        tableIsShow.value = true
      })
      return resolve(tempData)
    }
  }
}

/** 查询树 */
const treeLoading = ref(false)
const getTreeData = async () => {
  try {
    treeLoading.value = true
    const { code, data } = await querySystemTree({
      keyword: treeFilterText.value,
      searchRange: '7'
    })
    treeLoading.value = false
    if (code === '200') {
      treeData.value = data
      totalSystem.value = data.length
    }
  } catch (e) {
    treeLoading.value = false
  }
}

const handleSearch = debounce(async () => {
  // 重置数据
  try {
    loading.value = true

    // 重置图表为加载状态
    if (chartRef1.value && chartRef1.value.showLoading) {
      chartRef1.value.showLoading()
    }
    if (chartRef2.value && chartRef2.value.showLoading) {
      chartRef2.value.showLoading()
    }

    await getTreeData()
    breadcrumbList.value = breadcrumbList.value.slice(0, 1)
    radio.value = '返回列表' // 重置为列表展示
    selectedNode.value.id = '0'
    selectedNode.value.type = ''
    handleQueryStatistics('')
    handleQueryStatistics('1')
    handleQueryStatistics('2')
    handleTimeSpanBuildObjs([])
    loading.value = false
  } catch (e) {
    loading.value = false
  }
}, 300)

const filterNode = (value, data) => {
  if (!value) return true
  return data.text.indexOf(value) !== -1
}

const treeRef = ref(null)
const selectedNode = ref({
  id: '0',
  modelId: '',
  text: '',
  type: ''
}) // 选中节点

const handleNodeClick = (data, node) => {
  if (selectedNode.value.id === data.id) {
    return
  }

  radio.value = '返回列表' // 重置为列表展示
  selectedNode.value = data
  selectedNode.value.type =
    node.level === 1 ? 'system' : node.level === 2 ? 'model' : 'entity'
  selectedNode.value.modelId = node.level === 2 ? data.id : data.parentId

  if (node.level === 1) {
    handleQueryStatistics('', data.id)
    queryparams.systemId = data.id
    queryparams.currentPage = 1
    // handleQueryCatalogList()
    const modelIds = treeData.value
      .find((item) => item.id === data.id)
      .children.map((item) => item.id)

    handleQueryEntityAndColNum(modelIds, 'system')
    if (breadcrumbList.value.length === 1) {
      breadcrumbList.value.push(selectedNode.value)
    } else {
      breadcrumbList.value.splice(1, breadcrumbList.value.length)
      breadcrumbList.value.push(selectedNode.value)
    }
  }

  if (node.level === 2) {
    handleQueryEntityAndColNum(data.id, 'model')
    handleModelAndEntityDetail(data.id)
    if (breadcrumbList.value.length === 1) {
      breadcrumbList.value.push(...[node.parent.data, selectedNode.value])
    } else if (breadcrumbList.value.length === 2) {
      breadcrumbList.value.push(selectedNode.value)
    } else {
      breadcrumbList.value.splice(2, breadcrumbList.value.length)
      breadcrumbList.value.push(selectedNode.value)
    }
  }

  if (node.level === 3) {
    handleModelAndEntityDetail(data.parentId, data.id)
    selectedNode.value.modelName = data.parentText
    selectedNode.value.systemName = node.parent.parent.data.text
    if (breadcrumbList.value.length === 1) {
      breadcrumbList.value.push(
        ...[node.parent.parent.data, node.parent.data, selectedNode.value]
      )
    } else if (breadcrumbList.value.length === 2) {
      breadcrumbList.value.push(...[node.parent.data, selectedNode.value])
    } else if (breadcrumbList.value.length === 3) {
      breadcrumbList.value.push(selectedNode.value)
    } else {
      breadcrumbList.value.splice(3, breadcrumbList.value.length)
      breadcrumbList.value.push(selectedNode.value)
    }
  }
}

const handleClickTableName = (row) => {
  const node = treeRef.value.getNode(
    selectedNode.value.children.find((item) => item.id === row.entityId)
  )
  treeRef.value.setCurrentKey(node.data.uuid)
  selectedNode.value = node.data
  selectedNode.value.type = 'entity'
  selectedNode.value.modelId = row.modelId
  selectedNode.value.modelName = node.data.parentText
  selectedNode.value.systemName = node.parent.parent.data.text
  handleModelAndEntityDetail(row.modelId, row.entityId)

  breadcrumbList.value.push(selectedNode.value)
}

const handleClickModelName = (row) => {
  const node = treeRef.value.getNode(
    selectedNode.value.children.find((item) => item.id === row.id)
  )
  treeRef.value.setCurrentKey(node.data.uuid)
  selectedNode.value = node.data
  selectedNode.value.type = 'model'
  selectedNode.value.modelId = row.id
  selectedNode.value.modelName = node.data.text
  handleQueryEntityAndColNum(row.id, 'model')
  handleModelAndEntityDetail(row.id)
  breadcrumbList.value.push(selectedNode.value)
}

const tableIsShow = ref(true)
const columnTableRef = ref(null)
const loading = ref(false)
// 查询字段列表
const getColumnList = async () => {
  try {
    getSpaceId()
    tableIsShow.value = false
    loading.value = true
    const { code, data } = await queryColByEntityIds([selectedNode.value.id])
    if (code === '200') {
      columnDtoList.value = data[0].items.map((item) => {
        return {
          ...item,
          dataTypeCode: item.dataType
        }
      })
    }
    tableIsShow.value = true
    nextTick(() => {
      columnTableRef.value && columnTableRef.value.doLayout()
    })
    loading.value = false
  } catch (e) {
    loading.value = false
  }
}

const radio = ref('返回列表')
const iframeRef = ref(null)
const handleRadioChange = () => {
  iframeUrl.value = ''
  const params = {
    physicalModelId: selectedNode.value.modelId,
    physicalModelName: selectedNode.value.text
  }
  if (radio.value === '图形化界面展示') {
    let a = [
      'x-space-id=' + proxy.$store.state.system.spaceId,
      'viewType=0',
      'readonly=true'
    ]
    for (let key in params) {
      a.push(key + '=' + params[key])
    }
    iframeUrl.value = config.iframeToDateModel + '?' + a.join('&')
    iframeRef.value.contentWindow.location.replace(iframeUrl.value)
  }
}

const handleToView = () => {
  radio.value = '图形化界面展示'
  nextTick(() => {
    handleRadioChange()
  })
}
const spaceId = ref('')
const getSpaceId = async () => {
  spaceId.value = config.spaceId
  proxy.$store.commit('setSpaceId', spaceId.value)
  let spaceIdCookies = {}
  spaceIdCookies[proxy.$store.state.user.currentUser.userName] = spaceId.value
  if (Cookies.get('spaceId') !== JSON.stringify(spaceIdCookies)) {
    Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
  }
  // if (!sessionStorage.getItem('spaceId')) {
  //   sessionStorage.setItem('spaceId', spaceId.value)
  // }
}

const statisticsData = ref([
  {
    name: '数据库目录数',
    key: 'pdmCatalogTotal',
    count: 0
  },

  {
    name: '模型数',
    count: 0,
    key: 'modelTotal'
  },
  {
    name: '库表数',
    count: 0,
    key: 'tableTotal'
  },
  {
    name: '视图数',
    count: 0,
    key: 'viewTotal'
  },

  {
    name: '字段数',
    count: 0,
    key: 'columnTotal'
  }
  // {
  //   name: '已发布数据库目录数',
  //   count: 0,
  //   key: 'publishDataCatalogTotal'
  // },
  // {
  //   name: '共享资源数',
  //   count: 0,
  //   key: 'shareResourceTotal'
  // },
  // {
  //   name: '共享字段数',
  //   count: 0,
  //   key: 'shareColumnTotal'
  // }
])

const statisticsDataCopy = computed(() => {
  if (selectedNode.value.type === 'model') {
    return statisticsData.value.filter(
      (item) =>
        item.key === 'tableTotal' ||
        item.key === 'columnTotal' ||
        item.key === 'viewTotal'
    )
  } else {
    return statisticsData.value.filter((item, index) => index > 1)
  }
})

const chartRef1 = ref(null)
const chartRef2 = ref(null)
const handleQueryStatistics = async (val, systemId) => {
  const formData = new FormData()
  formData.append('statisticsFlg', val)
  formData.append('systemId', systemId)
  const { code, data } = await queryStatistics(val || systemId ? formData : '')
  if (code === '200') {
    if (val === '1') {
      chartRef1.value.init(data.barStatistics)
    } else if (val === '2') {
      // chartRef1.value.init(data.barStatistics) // 暂时不用 替换成handleTimeSpanBuildObjs方法
    } else {
      statisticsData.value = statisticsData.value.map((item) => {
        return {
          ...item,
          count: data[item.key]
        }
      })
    }
  }
}

const countType = ref('d')
// 实体变化趋势
const handleTimeSpanBuildObjs = async (val) => {
  getSpaceId()
  const { code, data } = await countTimeSpanBuildObjs({
    // 如果countType为d starttime为七天前，endtime为今天，如果countType为m starttime为七个月前，endtime为今天的后一天（左闭右开），时间格式为YYYY-MM-DD
    startTime:
      countType.value === 'd'
        ? moment().subtract(7, 'days').format('YYYY-MM-DD')
        : moment().subtract(7, 'months').format('YYYY-MM-DD'),
    endTime: moment().add(1, 'days').format('YYYY-MM-DD'),
    filterTypes: val,
    countType: countType.value
  })
  if (code === '200') {
    if (val.length === 0) {
      chartRef2.value && chartRef2.value.init(data)
    }
    // else if (val.length === 1 && val[0] === 'table') {
    //   chartRef2.value && chartRef2.value.init('table', data)
    // } else if (val.length === 1 && val[0] === 'view') {
    //   chartRef2.value && chartRef2.value.init('view', data)
    // }
  }
}

const handleCountTypeChange = () => {
  chartRef2.value.resetData()
  nextTick(() => {
    handleTimeSpanBuildObjs([])
    // handleTimeSpanBuildObjs(['table'])
    // handleTimeSpanBuildObjs(['view'])
  })
}

const columnDtoList = ref([]) // 字段列表
const catalogList = ref([]) // 目录列表
const entityDtoList = ref([]) // 实体列表
const total = ref(0) // 总数

const queryparams = reactive({
  currentPage: 1,
  pageSize: 10,
  systemId: ''
})

const handleSizeChange = (val) => {
  queryparams.pageSize = val
  handleQueryCatalogList()
}

const handleCurrentChange = (val) => {
  queryparams.currentPage = val
  handleQueryCatalogList()
}

const systemTableRef = ref(null)
// 查询目录列表
const handleQueryCatalogList = async () => {
  tableIsShow.value = false
  loading.value = true
  const { code, data } = await queryCatalogStatistics(queryparams)
  if (code === '200') {
    tableIsShow.value = true
    catalogList.value = data.records
    total.value = data.total
  }
  nextTick(() => {
    systemTableRef.value && systemTableRef.value.doLayout()
    tableIsShow.value = true
  })
  loading.value = false
}

// 查询实体和字段数量
const handleQueryEntityAndColNum = async (modelIds, type) => {
  if (type === 'system') {
    loading.value = true
  }
  const ids = type === 'model' ? modelIds : modelIds.toString()
  const { code, data } = await queryEntityAndColNum(ids)
  if (code === '200') {
    if (type === 'model') {
      statisticsData.value[2].count = data[ids].table || 0
      statisticsData.value[3].count = data[ids].view || 0
      statisticsData.value[4].count = data[ids].columns || 0
    } else {
      const index = treeData.value.findIndex(
        (item) => item.id === selectedNode.value.id
      )
      tableIsShow.value = false

      catalogList.value = treeData.value[index].children.map((item) => {
        return {
          ...item,
          tableTotal: data[item.id].table || 0,
          viewTotal: data[item.id].view || 0,
          columnTotal: data[item.id].columns || 0,
          buildState: data[item.id].buildState
        }
      })
      loading.value = false

      nextTick(() => {
        systemTableRef.value && systemTableRef.value.doLayout()
        tableIsShow.value = true
      })
    }
  }
}

// 查询实体详情
const handleModelAndEntityDetail = async (modelId, entityId) => {
  try {
    loading.value = true
    const { code, data } = await queryEntityDetail({
      modelId,
      entityId
    })

    if (code === '200') {
      if (data.entityList) {
        // 查询模型详情
        entityDtoList.value = data.entityList
      } else {
        // 查询实体详情
        selectedNode.value.buildTable = data.entityInfo.buildTable
        selectedNode.value.createTime = data.entityInfo.createTime
        selectedNode.value.rowsCount = data.entityInfo.rowsCount || 0
        selectedNode.value.enetityCode = data.entityInfo.enetityCode

        if (data.entityInfo.entityTypeCode !== 'table') {
          selectedNode.value.sql = data.entityInfo.sql
        } else {
          columnDtoList.value = data.entityInfo.tableInfo?.map((item) => {
            return {
              ...item,
              dataTypeCode: item.dataType
            }
          })
        }
      }

      tableIsShow.value = false
      nextTick(() => {
        tableIsShow.value = true
      })
    }
    loading.value = false
  } catch (e) {
    console.log('e', e)
    loading.value = false
  }
}

const breadcrumbList = ref([
  {
    text: config.breadCumbTitle,
    id: '0'
  }
])

const handleNavigate = (val, index) => {
  if (index === breadcrumbList.value.length - 1) {
    return
  }

  radio.value = '返回列表' // 重置为列表展示
  breadcrumbList.value = breadcrumbList.value.slice(0, index + 1)
  selectedNode.value = val

  if (index) {
    const node = treeRef.value.getNode(val.uuid)
    treeRef.value.setCurrentKey(node.data.uuid)
  }

  if (index === 1) {
    handleQueryStatistics('', val.id)
  }

  if (index === 2) {
    handleQueryEntityAndColNum(val.id, 'model')
  }

  if (index === 0) {
    handleQueryStatistics('')
    handleQueryStatistics('1')
    handleQueryStatistics('2')
    handleTimeSpanBuildObjs([])
    // handleTimeSpanBuildObjs(['table'])
    // handleTimeSpanBuildObjs(['view'])
  }
}

onActivated(() => {
  // 初始化图表为加载状态
  if (chartRef1.value && chartRef1.value.showLoading) {
    chartRef1.value.showLoading()
  }
  if (chartRef2.value && chartRef2.value.showLoading) {
    chartRef2.value.showLoading()
  }

  getTreeData()
  handleQueryStatistics('')
  handleQueryStatistics('1')
  handleQueryStatistics('2')
  handleTimeSpanBuildObjs([])
})
</script>

<style scoped lang="scss">
@import '../../../assets/global.scss';
@import '@/assets/list.scss';

.page-container {
  height: 100vh !important;
  .toolbarTree-wrapper {
    display: flex;
    flex-direction: column;
    /deep/ .el-scrollbar__wrap {
      overflow-x: auto;
    }
  }
  .title {
    display: flex;
    align-items: center;

    .ellis {
      max-width: 660px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.searchTotal {
  .totalName {
    margin-left: 18px !important;
  }
}

.data-list {
  margin-left: 10px;
  font-size: 16px;
  padding: 0 4px;
  border-radius: 4px;
  font-weight: 300;
  text-align: center;
  color: #fff;
  background-color: #ff8635;
}

.data-cont-wrap {
  /*height: calc(100vh - 12px);*/
  margin-top: 0;
  background-color: #fff;
  /deep/ .el-scrollbar__wrap {
    overflow-x: auto;
  }

  .listCont {
    padding: 6px 0 0 0 !important;
  }
}

.huiliu-list {
  @extend .data-list;
  background-color: #83c954;
}

.info-list {
  @extend .data-list;
  background-color: #3572ff;
}

.custom-tree-name {
  margin-right: 4px;
}

.line2 {
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  .itemMes {
    margin-bottom: 24px;
  }
  .sql {
    width: 100%;
    display: flex;
    &-input {
      flex: 1;
    }
  }
  .success {
    color: #67c23a !important;
  }
  .error {
    color: #f56c6c !important;
  }
  .normal {
    color: #909399 !important;
  }
}

.chartOrTable {
  /deep/ .el-radio-button {
    .el-radio-button__inner {
      border-radius: 34px;
      background-color: #3572ff;
      color: #fff;
    }
  }
}

.el-collapse-title {
  display: flex;
  align-items: center;
  height: 24px;

  .ellis {
    font-size: 18px;
    color: #333;
    font-weight: 700;
    max-width: 660px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .info-list {
    display: flex;
    align-items: center;
    height: 100%;
  }
}

// /deep/ .el-table--scrollable-y .el-table__body-wrapper {
//   overflow: hidden !important;
// }

.iframe-wrap {
  width: 100%;
  height: calc(100vh - 76px);
  /*margin-left: 10px;*/
  /*padding-left: 10px;*/
  /*background-color: #fff;*/

  .iframe {
    height: 100%;
    width: 100%;
    border: none;
  }
}

.statisticsCont {
  /*display: flex;*/
  /*flex-direction: column;*/
  background-color: #fff;
  /*height: calc(100% - 12px);*/
  box-sizing: border-box;
  &-top {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    flex-wrap: wrap;
    margin-bottom: 24px;

    .workItem {
      display: flex;
      width: 21%;
      margin-bottom: 12px;
      margin-right: 12px;
      .content {
        width: 100%;
        padding: 12px;
        border-radius: 4px;
        border: 1px solid rgba(206, 206, 206, 0.43);
        background: linear-gradient(180deg, #f2f2f2 0%, #fafdff 100%);
        .name {
          font-size: 18px;
          color: #333;
          font-weight: 600;
        }
        .count {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          font-size: 24px;
          color: #333;
          font-weight: 600;
          span:nth-child(1) {
            margin-right: 8px;
            font-weight: 700px;
            color: #3572ff;
          }
          span:nth-child(2) {
            font-weight: 500;
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }

  &-bottom {
    /*flex: 1;*/
    width: 100%;
    display: flex;
    justify-content: space-between;
    .chartCont {
      height: 600px;
      width: 50%;
    }
  }
}

.marginLeft10 {
  margin-left: 12px;
}

.filterInput {
  /deep/ .el-input__inner {
    height: 38px;
  }
}

.page-header {
  height: 32px !important;
  display: flex;
  align-items: center;
}

>>> .el-table .el-table__header tr {
  background-color: #4eacfe;
}
>>> .el-table .el-table__header th {
  color: #fff;
  background-color: #4eacfe;
}

/* 目录页面分割条样式 */
.catalog-split-bar {
  background: linear-gradient(to right, #f5f7fa, #e8ecf0, #f5f7fa);
  border-left: 1px solid #d3d4d6;
  border-right: 1px solid #d3d4d6;
  transition: all 0.3s ease;
  height: 100% !important;
  min-height: 100vh;
  position: relative;
}

.catalog-split-bar:hover {
  background: linear-gradient(to right, #e8ecf0, #d3d4d6, #e8ecf0);
  border-left: 1px solid #b4bccc;
  border-right: 1px solid #b4bccc;
}

/* 确保分割条内部元素垂直居中 */
.catalog-split-bar .splitBar {
  height: 100% !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 调整分割条内部符号颜色 */
.catalog-split-bar .splitBar span {
  color: #909399 !important;
  transition: color 0.3s ease;
  visibility: visible !important;
  opacity: 0.6;
}

.catalog-split-bar:hover .splitBar span {
  color: #606266 !important;
  opacity: 1;
}

/* 添加分割条中央的视觉提示线 */
.catalog-split-bar::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #c0c4cc, transparent);
  border-radius: 1px;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.catalog-split-bar:hover::before {
  background: linear-gradient(to bottom, transparent, #909399, transparent);
  opacity: 1;
}
</style>
